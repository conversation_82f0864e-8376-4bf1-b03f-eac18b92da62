options:
    prefix: &e&lKHUYẾN MÃI &8»&r
    admin_permission: "khuyenmai.admin"
    currency_symbol: "Points"
    
    # C<PERSON>u hình khuyến mãi
    enable_banking_promo: true
    enable_card_promo: true

#================================================================
# HÀM XỬ LÝ KHUYẾN MÃI CÁ NHÂN
#================================================================
function applyPersonalPromotion(player: player, amount: number, payment_type: text):
    # Xóa metadata cũ trước khi kiểm tra
    clear {_player}'s metadata "simppay_personal_promo_active"

    if {personal.promo::%{_player}'s uuid%} is not set:
        stop

    set {_duration} to {personal.promo.duration::%{_player}'s uuid%}
    if {_duration} is not set:
        set {_duration} to 24

    set {_start_time} to {personal.promo.time::%{_player}'s uuid%}
    set {_expire} to {_start_time}
    add ({_duration} * 1 hour) to {_expire}

    if now > {_expire}:
        send "{@prefix} &cKhuyến mãi cá nhân của bạn đã hết hạn!" to {_player}
        delete {personal.promo::%{_player}'s uuid%}
        delete {personal.promo.percent::%{_player}'s uuid%}
        delete {personal.promo.time::%{_player}'s uuid%}
        delete {personal.promo.type::%{_player}'s uuid%}
        delete {personal.promo.duration::%{_player}'s uuid%}
        delete {personal.promo.name::%{_player}'s uuid%}
        stop

    set {_promo_type} to {personal.promo.type::%{_player}'s uuid%}
    if {_promo_type} is not "all":
        if {_promo_type} is not {_payment_type}:
            stop

    set metadata value "simppay_personal_promo_active" of {_player} to true

    set {_base_coins} to round({_amount} / 1000)
    set {_multiplier} to {personal.promo::%{_player}'s uuid%}
    set {_bonus_percent} to {personal.promo.percent::%{_player}'s uuid%}

    set {_total_coins} to round({_base_coins} * {_multiplier})
    set {_bonus_coins} to {_total_coins} - {_base_coins}

    execute console command "points give %{_player}'s name% %{_total_coins}%"

    if {_amount} >= 1000000:
        set {_millions} to round({_amount} / 1000000)
        set {_formatted_amount} to "%{_millions}%M VND"
    else if {_amount} >= 1000:
        set {_thousands} to round({_amount} / 1000)
        set {_formatted_amount} to "%{_thousands}%k VND"
    else:
        set {_formatted_amount} to "%{_amount}% VND"

    log "[PersonalPromo] Gave %{_total_coins}% points to %{_player}'s name% (%{_bonus_percent}% percent bonus from %{_formatted_amount}% via %{_payment_type}%)" to "personal-promo-coins.log"

    send "" to {_player}
    send "{@prefix} &a&lKHUYẾN MÃI CÁ NHÂN THÀNH CÔNG!" to {_player}
    send "&fBạn đã nạp &e%{_formatted_amount}%&f qua &b%{_payment_type}%&f." to {_player}
    send "&fXu gốc: &a%{_base_coins}% %{@currency_symbol}%&f, Thưởng: &d+%{_bonus_coins}%%% %{@currency_symbol}%&f" to {_player}
    send "&fTổng nhận: &a&l%{_total_coins}% %{@currency_symbol}%&f (&d&l+%{_bonus_percent}%%%&f)" to {_player}
    send "" to {_player}

    log "[SimpPayHook] Player %{_player}'s name% used personal promo: %{_formatted_amount}% -> %{_total_coins}% points via %{_payment_type}%" to "simppay-promo.log"

    delete {personal.promo::%{_player}'s uuid%}
    delete {personal.promo.percent::%{_player}'s uuid%}
    delete {personal.promo.time::%{_player}'s uuid%}
    delete {personal.promo.type::%{_player}'s uuid%}
    delete {personal.promo.duration::%{_player}'s uuid%}
    delete {personal.promo.name::%{_player}'s uuid%}



command /simppay-hook <text> <text> <number> <text>:
    permission: op
    trigger:
        if arg-1 is "success":
            set {_player} to arg-2 parsed as player
            set {_amount} to arg-3
            set {_type} to arg-4

            # Log để debug
            log "[SimpPayHook] Received hook: player=%arg-2%, amount=%{_amount}%, type=%{_type}%" to "simppay-hook-debug.log"

            if {_player} is not set:
                log "[SimpPayHook] Player %arg-2% not found or offline" to "simppay-hook-debug.log"
                stop

            # Kiểm tra và xử lý khuyến mãi cá nhân
            if {_type} contains "BANKING":
                if {@enable_banking_promo} is true:
                    log "[SimpPayHook] Processing banking promo for %{_player}%" to "simppay-hook-debug.log"
                    applyPersonalPromotion({_player}, {_amount}, "banking")
                else:
                    log "[SimpPayHook] Banking promo disabled" to "simppay-hook-debug.log"
            else if {_type} contains "CARD":
                if {@enable_card_promo} is true:
                    log "[SimpPayHook] Processing card promo for %{_player}%" to "simppay-hook-debug.log"
                    applyPersonalPromotion({_player}, {_amount}, "card")
                else:
                    log "[SimpPayHook] Card promo disabled" to "simppay-hook-debug.log"
            else:
                log "[SimpPayHook] Unknown payment type: %{_type}%" to "simppay-hook-debug.log"

on script load:
    set {simppay.hook.enabled} to true

command /simppay-event <text> <text> <number> <text>:
    permission: op
    trigger:
        if arg-1 is "PaymentSuccess":
            set {_player_name} to arg-2
            set {_amount} to arg-3
            set {_payment_type} to arg-4
            set {_player} to {_player_name} parsed as player
            
            if {_player} is not set:
                stop
            
            # Xử lý theo loại thanh toán
            if {_payment_type} contains "BANKING":
                if {@enable_banking_promo} is true:
                    applyPersonalPromotion({_player}, {_amount}, "banking")
            else if {_payment_type} contains "CARD":
                if {@enable_card_promo} is true:
                    applyPersonalPromotion({_player}, {_amount}, "card")

command /promo-give-coins <text> <number> <number> <text> <number>:
    permission: op
    trigger:
        set {_player_name} to arg-1
        set {_bonus_amount} to arg-2
        set {_original_amount} to arg-3
        set {_payment_type} to arg-4
        set {_bonus_percent} to arg-5
        set {_player} to {_player_name} parsed as player
        
        if {_player} is not set:
            stop
        
        execute console command "points give %{_player_name}% %{_bonus_amount}%"

        log "[PersonalPromo] Gave %{_bonus_amount}% points to %{_player_name}% (%%{_bonus_percent}%% bonus from %{_original_amount}% via %{_payment_type}%)" to "personal-promo-coins.log"

#================================================================
# LỆNH CHO ADMIN TẠO KHUYẾN MÃI CÁ NHÂN
#================================================================
command /promoadmin [<text>] [<text>] [<text>] [<text>] [<text>]:
    permission: {@admin_permission}
    trigger:
        if arg-1 is not set:
            send "{@prefix} &fLệnh quản lý khuyến mãi cá nhân:"
            send "&a/promoadmin create <player> <percent> <duration_hours> [type] &7- Tạo KM cá nhân"
            send "&7  - type: banking, card, all (mặc định: all)"
            send "&7  - Ví dụ: /promoadmin create Steve 50 24 banking"
            send "&a/promoadmin check <player> &7- Kiểm tra KM của người chơi"
            send "&a/promoadmin remove <player> &7- Xóa KM của người chơi"
            send "&a/promoadmin list &7- Liệt kê tất cả KM đang hoạt động"
            send "&a/promoadmin debug <player> &7- Debug thông tin KM"
            send "&a/promoadmin reload &7- Tải lại Skript"
            send "&a/promoadmin test <player> <amount> <type> &7- Test event"
            stop

        if arg-1 is "create":
            if arg-2 is not set:
                send "{@prefix} &cCú pháp: /promoadmin create <player> <percent> <duration_hours> [type]"
                stop
            if arg-3 is not set:
                send "{@prefix} &cCú pháp: /promoadmin create <player> <percent> <duration_hours> [type]"
                stop
            if arg-4 is not set:
                send "{@prefix} &cCú pháp: /promoadmin create <player> <percent> <duration_hours> [type]"
                stop
            
            set {_player_name} to arg-2
            set {_percent} to arg-3 parsed as number
            set {_duration} to arg-4 parsed as number
            set {_type} to arg-5
            if {_type} is not set:
                set {_type} to "all"
            
            if {_type} is not "banking":
                if {_type} is not "card":
                    if {_type} is not "all":
                        send "{@prefix} &cLoại thanh toán chỉ có thể là: banking, card, all"
                        stop
            
            set {_target_player} to {_player_name} parsed as offline player
            if {_target_player} is not set:
                send "{@prefix} &cKhông tìm thấy người chơi &e%{_player_name}%&c!"
                stop
            
            set {_uuid} to {_target_player}'s uuid
            
            set {_multiplier} to 1 + ({_percent} / 100)
            
            set {personal.promo::%{_uuid}%} to {_multiplier}
            set {personal.promo.percent::%{_uuid}%} to {_percent}
            set {personal.promo.time::%{_uuid}%} to now
            set {personal.promo.type::%{_uuid}%} to {_type}
            set {personal.promo.duration::%{_uuid}%} to {_duration}
            set {personal.promo.name::%{_uuid}%} to {_player_name}
            
            send "{@prefix} &aĐã tạo khuyến mãi cá nhân cho &e%{_player_name}%&a:"
            send "&7- Mức thưởng: &e%{_percent}%%%"
            send "&7- Thời gian: &e%{_duration}% giờ"
            send "&7- Loại thanh toán: &e%{_type}%"
            
            # Fix: Check if player is online properly
            set {_online_player} to {_player_name} parsed as player
            if {_online_player} is set:
                send "" to {_online_player}
                send "{@prefix} &a&lBạn đã nhận được khuyến mãi cá nhân!" to {_online_player}
                send "&fMức thưởng: &e&l%{_percent}%%%&f cho &b%{_type}%" to {_online_player}
                send "&fThời gian: &e%{_duration}% giờ" to {_online_player}
                send "&fHãy nạp thẻ để nhận thưởng!" to {_online_player}
                send "" to {_online_player}
                send "&7(Đã gửi thông báo cho người chơi)"
            else:
                send "&7(Người chơi không online, không gửi thông báo)"
            stop

        if arg-1 is "test":
            if arg-2 is not set:
                send "{@prefix} &cCú pháp đúng: /promoadmin test <player> <amount> <type>"
                send "&7Ví dụ: /promoadmin test Steve 10000 banking"
                stop
            if arg-3 is not set:
                send "{@prefix} &cCú pháp đúng: /promoadmin test <player> <amount> <type>"
                send "&7Ví dụ: /promoadmin test Steve 10000 banking"
                stop
            if arg-4 is not set:
                send "{@prefix} &cCú pháp đúng: /promoadmin test <player> <amount> <type>"
                send "&7Ví dụ: /promoadmin test Steve 10000 banking"
                stop

            set {_player_name} to arg-2
            set {_amount} to arg-3 parsed as number
            set {_type} to arg-4

            set {_target_player} to {_player_name} parsed as offline player
            if {_target_player} is not set:
                send "{@prefix} &cKhông tìm thấy người chơi &e%{_player_name}%&c! (Có thể tên sai hoặc người chơi chưa từng vào server)"
                send "&7Hãy chắc chắn rằng player đã từng vào server ít nhất 1 lần."
                stop

            set {_uuid} to {_target_player}'s uuid
            if {personal.promo::%{_uuid}%} is not set:
                send "{@prefix} &cNgười chơi &e%{_player_name}% &ckhông có khuyến mãi cá nhân nào để test!"
                send "&7Hãy tạo khuyến mãi cá nhân trước bằng lệnh /promoadmin create."
                stop

            send "{@prefix} &aTesting hook với:"
            send "&7- Player: &e%{_player_name}%"
            send "&7- Amount: &e%{_amount}%"
            send "&7- Type: &e%{_type}%"
            execute console command "simppay-hook success %{_player_name}% %{_amount}% %{_type}%"
            stop

        if arg-1 is "check":
            if arg-2 is not set:
                send "{@prefix} &cCú pháp: /promoadmin check <player>"
                stop
            
            set {_player_name} to arg-2
            set {_target_player} to {_player_name} parsed as offline player
            if {_target_player} is not set:
                send "{@prefix} &cKhông tìm thấy người chơi &e%{_player_name}%&c!"
                stop
            
            set {_uuid} to {_target_player}'s uuid
            
            if {personal.promo::%{_uuid}%} is not set:
                send "{@prefix} &cNguời chơi &e%{_player_name}% &ckhông có khuyến mãi cá nhân."
                stop
            
            # Fix: Kiểm tra duration có tồn tại không
            set {_duration} to {personal.promo.duration::%{_uuid}%}
            if {_duration} is not set:
                send "{@prefix} &cDữ liệu khuyến mãi của &e%{_player_name}% &cbị lỗi (thiếu duration)."
                stop
            
            set {_start_time} to {personal.promo.time::%{_uuid}%}
            set {_expire} to {_start_time}
            add ({_duration} * 1 hour) to {_expire}
            
            if now > {_expire}:
                send "{@prefix} &cKhuyến mãi của &e%{_player_name}% &cđã hết hạn!"
                # Xóa dữ liệu hết hạn
                delete {personal.promo::%{_uuid}%}
                delete {personal.promo.percent::%{_uuid}%}
                delete {personal.promo.time::%{_uuid}%}
                delete {personal.promo.type::%{_uuid}%}
                delete {personal.promo.duration::%{_uuid}%}
                delete {personal.promo.name::%{_uuid}%}
                stop
            
            set {_timeLeft} to difference between {_expire} and now
            send "{@prefix} &fThông tin khuyến mãi của &e%{_player_name}%&f:"
            send "&7- Mức thưởng: &e%{personal.promo.percent::%{_uuid}%}%%%"
            send "&7- Áp dụng cho: &e%{personal.promo.type::%{_uuid}%}%"
            send "&7- Thời gian còn lại: &a%{_timeLeft}%"
            stop

        if arg-1 is "remove":
            if arg-2 is not set:
                send "{@prefix} &cCú pháp: /promoadmin remove <player>"
                stop
            
            set {_player_name} to arg-2
            set {_target_player} to {_player_name} parsed as offline player
            if {_target_player} is not set:
                send "{@prefix} &cKhông tìm thấy người chơi &e%{_player_name}%&c!"
                stop
            
            set {_uuid} to {_target_player}'s uuid
            
            if {personal.promo::%{_uuid}%} is not set:
                send "{@prefix} &cNguời chơi &e%{_player_name}% &ckhông có khuyến mãi cá nhân."
                stop
            
            delete {personal.promo::%{_uuid}%}
            delete {personal.promo.percent::%{_uuid}%}
            delete {personal.promo.time::%{_uuid}%}
            delete {personal.promo.type::%{_uuid}%}
            delete {personal.promo.duration::%{_uuid}%}
            delete {personal.promo.name::%{_uuid}%}
            
            send "{@prefix} &aĐã xóa khuyến mãi cá nhân của &e%{_player_name}%&a!"
            stop

        if arg-1 is "list":
            send "{@prefix} &fDanh sách khuyến mãi cá nhân đang hoạt động:"
            set {_count} to 0
            loop {personal.promo::*}:
                set {_uuid} to loop-index
                set {_multiplier} to loop-value
                set {_percent} to {personal.promo.percent::%{_uuid}%}
                set {_type} to {personal.promo.type::%{_uuid}%}
                set {_duration} to {personal.promo.duration::%{_uuid}%}
                set {_start_time} to {personal.promo.time::%{_uuid}%}
                set {_playerName} to {personal.promo.name::%{_uuid}%}
                
                # Fix: Kiểm tra đầy đủ các giá trị trước khi xử lý
                if {_duration} is not set:
                    send "DEBUG: uuid=%{_uuid}% không có duration, bỏ qua!" to console
                    continue
                
                if {_start_time} is not set:
                    send "DEBUG: uuid=%{_uuid}% không có start time, bỏ qua!" to console
                    continue
                
                set {_expire} to {_start_time}
                add ({_duration} * 1 hour) to {_expire}
                
                if now <= {_expire}:
                    add 1 to {_count}
                    set {_timeLeft} to difference between {_expire} and now
                    
                    # Fix: Always show stored player name
                    if {_playerName} is set:
                        send "&7- &e%{_playerName}% &7(%{_percent}%%%, %{_type}%, %{_timeLeft}%)"
                    else:
                        # Fallback for old entries without stored name
                        set {_offlinePlayer} to {_uuid} parsed as offline player
                        if {_offlinePlayer} is set:
                            set {_parsedName} to name of {_offlinePlayer}
                            send "&7- &e%{_parsedName}% &7(%{_percent}%%%, %{_type}%, %{_timeLeft}%)"
                        else:
                            set {_shortUUID} to first 8 characters of {_uuid}
                            send "&7- &ePlayer_%{_shortUUID}% &7(%{_percent}%%%, %{_type}%, %{_timeLeft}%)"
                else:
                    send "DEBUG: uuid=%{_uuid}% đã hết hạn, bỏ qua!" to console
                    
            if {_count} is 0:
                send "&7Không có khuyến mãi nào đang hoạt động."
            stop

        if arg-1 is "debug":
            if arg-2 is not set:
                send "{@prefix} &cCú pháp: /promoadmin debug <player>"
                stop
            
            set {_player_name} to arg-2
            set {_target_player} to {_player_name} parsed as offline player
            if {_target_player} is not set:
                send "{@prefix} &cKhông tìm thấy người chơi &e%{_player_name}%&c!"
                stop
            
            set {_uuid} to {_target_player}'s uuid
            
            send "{@prefix} &fDebug thông tin khuyến mãi của &e%{_player_name}%&f:"
            send "&7- UUID: &e%{_uuid}%"
            send "&7- Multiplier: &e%{personal.promo::%{_uuid}%}%"
            send "&7- Percent: &e%{personal.promo.percent::%{_uuid}%}%"
            send "&7- Duration: &e%{personal.promo.duration::%{_uuid}%}%"
            send "&7- Type: &e%{personal.promo.type::%{_uuid}%}%"
            send "&7- Name: &e%{personal.promo.name::%{_uuid}%}%"
            send "&7- Start time: &e%{personal.promo.time::%{_uuid}%}%"
            send "&7- Current time: &e%now%"
            
            if {personal.promo.time::%{_uuid}%} is set:
                if {personal.promo.duration::%{_uuid}%} is set:
                    set {_duration} to {personal.promo.duration::%{_uuid}%}
                    set {_start_time} to {personal.promo.time::%{_uuid}%}
                    set {_expire} to {_start_time}
                    add ({_duration} * 1 hour) to {_expire}
                    send "&7- Expire time: &e%{_expire}%"
                    if now > {_expire}:
                        send "&7- Is expired: &etrue"
                    else:
                        send "&7- Is expired: &efalse"
            stop

        if arg-1 is "reload":
            execute console command "sk reload scripts"
            send "{@prefix} &aĐã tải lại tất cả Skript!"
            stop

#================================================================
# LỆNH CHO NGƯỜI CHƠI KIỂM TRA
#================================================================
command /checkpromo:
    aliases: /kiemtrapromo, /mypromo
    trigger:
        send "&7[DEBUG] Checking promo for player: %player's name% (UUID: %player's uuid%)" to player
        send "&7[DEBUG] Promo data: %{personal.promo::%player's uuid%}%" to player
        
        if {personal.promo::%player's uuid%} is not set:
            send "{@prefix} &cBạn không có khuyến mãi cá nhân nào."
            stop
        
        # Fix: Kiểm tra duration có tồn tại không
        set {_duration} to {personal.promo.duration::%player's uuid%}
        if {_duration} is not set:
            send "{@prefix} &cDữ liệu khuyến mãi của bạn bị lỗi!"
            stop
        
        set {_start_time} to {personal.promo.time::%player's uuid%}
        set {_expire} to {_start_time}
        add ({_duration} * 1 hour) to {_expire}
        
        if now > {_expire}:
            send "{@prefix} &cKhuyến mãi cá nhân của bạn đã hết hạn!"
            delete {personal.promo::%player's uuid%}
            delete {personal.promo.percent::%player's uuid%}
            delete {personal.promo.time::%player's uuid%}
            delete {personal.promo.type::%player's uuid%}
            delete {personal.promo.duration::%player's uuid%}
            delete {personal.promo.name::%player's uuid%}
            stop
        
        set {_timeLeft} to difference between {_expire} and now
        send ""
        send "{@prefix} &fThông tin khuyến mãi cá nhân của bạn:"
        send "&7- Mức thưởng: &e&l%{personal.promo.percent::%player's uuid%}%%%"
        send "&7- Áp dụng cho: &e%{personal.promo.type::%player's uuid%}%"
        send "&7- Thời gian còn lại: &a%{_timeLeft}%"
        send ""