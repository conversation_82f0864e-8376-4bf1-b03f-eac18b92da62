package org.simpmc.simppay.handler.banking.payos;

import lombok.NoArgsConstructor;
import org.bukkit.Bukkit;
import org.simpmc.simppay.config.ConfigManager;
import org.simpmc.simppay.config.types.BankingConfig;
import org.simpmc.simppay.config.types.banking.PayosConfig;
import org.simpmc.simppay.data.PaymentStatus;
import org.simpmc.simppay.data.bank.payos.PayosAdapter;
import org.simpmc.simppay.event.PaymentBankPromptEvent;
import org.simpmc.simppay.event.PaymentQueueSuccessEvent;
import org.simpmc.simppay.handler.PaymentHandler;
import org.simpmc.simppay.handler.banking.data.BankingData;
import org.simpmc.simppay.model.Payment;
import org.simpmc.simppay.model.PaymentResult;
import org.simpmc.simppay.model.detail.BankingDetail;
import org.simpmc.simppay.model.detail.PaymentDetail;
import org.simpmc.simppay.util.MessageUtil;

import vn.payos.PayOS;
import vn.payos.type.CheckoutResponseData;
import vn.payos.type.PaymentData;
import vn.payos.type.PaymentLinkData;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

// Helper class to carry both orderCode and response
class PaymentCreateResult {
    public final long orderCode;
    public final CheckoutResponseData response;
    
    public PaymentCreateResult(long orderCode, CheckoutResponseData response) {
        this.orderCode = orderCode;
        this.response = response;
    }
}

@NoArgsConstructor
public class PayosHandlerV2 implements PaymentHandler {
    private static final String RETURN_CANCEL_URL = "https://payos.vn";
    private PayOS payOS;

    private PayOS getPayOSClient() {
        if (payOS == null) {
            PayosConfig config = ConfigManager.getInstance().getConfig(PayosConfig.class);
            if (config.clientId == null || config.apiKey == null || config.checksumKey == null) {
                MessageUtil.info("[PayOS-V2] Thiếu thông tin cấu hình PayOS");
                return null;
            }
            try {
                payOS = new PayOS(config.clientId, config.apiKey, config.checksumKey);
                MessageUtil.info("[PayOS-V2] Khởi tạo PayOS SDK thành công");
            } catch (Exception e) {
                MessageUtil.info("[PayOS-V2] Lỗi khởi tạo PayOS SDK: " + e.getMessage());
                return null;
            }
        }
        return payOS;
    }

    @Override
    public PaymentStatus processPayment(Payment payment) {
        BankingDetail detail = (BankingDetail) payment.getDetail();
        
        PaymentCreateResult result;
        try {
            result = createPaymentLink(detail).get();
        } catch (InterruptedException | ExecutionException e) {
            MessageUtil.debug("[PayOS-V2-ProcessPayment] Exception: " + e.getMessage());
            return PaymentStatus.FAILED;
        }
        
        if (result == null || result.response == null) {
            MessageUtil.debug("[PayOS-V2-ProcessPayment] Response is null");
            return PaymentStatus.FAILED;
        }
        
        CheckoutResponseData response = result.response;
        MessageUtil.debug("[PayOS-V2-ProcessPayment] Success: " + response.getCheckoutUrl());
        
        // Set order code as reference
        String refID = String.valueOf(result.orderCode);
        payment.getDetail().setRefID(refID);
        
        // Trigger success event
        Bukkit.getPluginManager().callEvent(new PaymentQueueSuccessEvent(payment));
        
        // Create banking data for prompt event
        BankingData bankData = BankingData.builder()
                .bin(response.getBin())
                .playerUUID(payment.getPlayerUUID())
                .desc(response.getDescription())
                .amount(response.getAmount())
                .url(response.getCheckoutUrl())
                .accountNumber(response.getAccountNumber())
                .qrString(response.getQrCode())
                .build();
        
        Bukkit.getPluginManager().callEvent(new PaymentBankPromptEvent(bankData));
        return PaymentStatus.PENDING;
    }

    @Override
    public PaymentResult getTransactionResult(PaymentDetail detail) {
        PaymentLinkData paymentData;
        try {
            paymentData = getPaymentInfo(detail.getRefID()).get();
        } catch (ExecutionException | InterruptedException e) {
            MessageUtil.debug("[PayOS-V2-GetTransactionResult] Exception: " + e.getMessage());
            return new PaymentResult(PaymentStatus.FAILED, 0, null);
        }
        
        if (paymentData == null) {
            MessageUtil.debug("[PayOS-V2-GetTransactionResult] Payment data is null");
            return new PaymentResult(PaymentStatus.FAILED, 0, null);
        }
        
        // MessageUtil.debug("[PayOS-V2-GetTransactionResult] Status: " + paymentData.getStatus()); // Tắt để tránh spam
        
        PaymentStatus status = PayosAdapter.getStatus(paymentData.getStatus());
        
        // Chỉ log khi status khác PENDING để giảm spam console
        if (status != PaymentStatus.PENDING) {
            MessageUtil.debug("[PayOS-V2-GetTransactionResult] Final Status: " + status);
        }
        
        return new PaymentResult(status, paymentData.getAmount(), null);
    }

    @Override
    public PaymentStatus cancel(Payment payment) {
        try {
            PaymentLinkData response = cancelPaymentLink(payment.getDetail().getRefID()).get();
            
            if (response == null) {
                MessageUtil.debug("[PayOS-V2-Cancel] Response is null");
                return PaymentStatus.FAILED;
            }
            
            MessageUtil.debug("[PayOS-V2-Cancel] Success: " + response.getStatus());
            
            PaymentStatus status = PayosAdapter.getStatus(response.getStatus());
            return status == PaymentStatus.CANCELLED ? PaymentStatus.CANCELLED : PaymentStatus.FAILED;
            
        } catch (InterruptedException | ExecutionException e) {
            MessageUtil.debug("[PayOS-V2-Cancel] Exception: " + e.getMessage());
            return PaymentStatus.FAILED;
        }
    }

    private CompletableFuture<PaymentCreateResult> createPaymentLink(BankingDetail detail) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PayOS client = getPayOSClient();
                if (client == null) {
                    return null;
                }
                
                BankingConfig bankConfig = ConfigManager.getInstance().getConfig(BankingConfig.class);
                
                // Tạo orderCode unique bằng timestamp để tránh trùng lặp
                long orderCode = System.currentTimeMillis() / 1000L; // Unix timestamp
                
                // Nếu vẫn trùng thì thêm random
                if (orderCode <= 12) { // Nếu orderCode quá nhỏ hoặc trùng với các ID đã dùng
                    orderCode = System.currentTimeMillis() / 1000L + (long)(Math.random() * 1000);
                }
                
                // Tạo payment data với mô tả ngắn gọn như PayosHandler cũ
                String description = "payos";  // Giữ nguyên như PayosHandler cũ
                
                PaymentData paymentData = PaymentData.builder()
                        .orderCode(orderCode)
                        .amount((int) detail.getAmount())
                        .description(description)
                        .returnUrl(RETURN_CANCEL_URL)
                        .cancelUrl(RETURN_CANCEL_URL)
                        .expiredAt(System.currentTimeMillis() / 1000L + bankConfig.bankingTimeout) // Timeout như PayosHandler cũ
                        // Không tạo item như PayosHandler cũ
                        .build();
                
                MessageUtil.debug("[PayOS-V2-CreatePaymentLink] OrderCode: " + orderCode + ", Amount: " + detail.getAmount());
                
                CheckoutResponseData result = client.createPaymentLink(paymentData);
                MessageUtil.debug("[PayOS-V2-CreatePaymentLink] Created successfully: " + result.getPaymentLinkId());
                
                return new PaymentCreateResult(orderCode, result);
                
            } catch (Exception e) {
                MessageUtil.debug("[PayOS-V2-CreatePaymentLink] Error: " + e.getMessage());
                e.printStackTrace();
                return null;
            }
        });
    }
    
    private CompletableFuture<PaymentLinkData> getPaymentInfo(String paymentLinkId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PayOS client = getPayOSClient();
                if (client == null) {
                    return null;
                }
                
                // Convert paymentLinkId to long for orderCode
                long orderCode = Long.parseLong(paymentLinkId);
                PaymentLinkData result = client.getPaymentLinkInformation(orderCode);
                
                // Chỉ log khi không phải PENDING để giảm spam
                if (!"PENDING".equals(result.getStatus())) {
                    MessageUtil.debug("[PayOS-V2-GetPaymentInfo] Status: " + result.getStatus());
                }
                return result;
                
            } catch (Exception e) {
                MessageUtil.debug("[PayOS-V2-GetPaymentInfo] Error: " + e.getMessage());
                return null;
            }
        });
    }
    
    private CompletableFuture<PaymentLinkData> cancelPaymentLink(String paymentLinkId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PayOS client = getPayOSClient();
                if (client == null) {
                    return null;
                }
                
                // Convert paymentLinkId to long for orderCode
                long orderCode = Long.parseLong(paymentLinkId);
                PaymentLinkData result = client.cancelPaymentLink(orderCode, "Cancelled by user");
                
                MessageUtil.debug("[PayOS-V2-CancelPaymentLink] Status: " + result.getStatus());
                return result;
                
            } catch (Exception e) {
                MessageUtil.debug("[PayOS-V2-CancelPaymentLink] Error: " + e.getMessage());
                return null;
            }
        });
    }
}
