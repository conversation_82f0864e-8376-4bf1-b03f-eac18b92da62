import org.glavo.rcon.Rcon

plugins {
    id 'java'
    id 'com.gradleup.shadow'
}

repositories {
    maven {
        name = "tcoded-releases"
        url = "https://repo.tcoded.com/releases"
    }
}

version = getRootProject().version
dependencies {
    // Include the common module
    implementation project(':simppay-api')
    compileOnly 'org.projectlombok:lombok:1.18.38'
    annotationProcessor 'org.projectlombok:lombok:1.18.38'
    compileOnly 'com.h2database:h2:2.3.232'
    compileOnly "io.papermc.paper:paper-api:1.21.4-R0.1-SNAPSHOT"
    compileOnly 'com.j256.ormlite:ormlite-jdbc:6.1'

    compileOnly 'org.black_ixx:playerpoints:3.3.0'

    compileOnly('me.clip:placeholderapi:2.11.6') {
        exclude group: 'me.clip.placeholderapi', module: 'libs'
    }
    compileOnly('org.geysermc.floodgate:api:2.2.4-SNAPSHOT')
    implementation("de.exlll:configlib-paper:4.6.0") {
        exclude group: 'org.yaml', module: 'snakeyaml-engine'
    }
    implementation "dev.jorel:commandapi-bukkit-shade:9.7.0"
    implementation("com.tcoded:FoliaLib:0.5.1")
    implementation('me.devnatan:inventory-framework-platform-bukkit:3.3.8')
    implementation 'me.devnatan:inventory-framework-anvil-input:3.3.8'
    implementation('me.devnatan:inventory-framework-platform-paper:3.3.8')
    implementation("com.github.retrooper:packetevents-spigot:2.8.0")
    implementation('net.wesjd:anvilgui:1.10.5-SNAPSHOT')
    
    // PayOS SDK
    implementation 'vn.payos:payos-java:1.0.3'
}

shadowJar {
    // Configure name & output location
    archiveBaseName.set("SimpPay-Paper")
    archiveVersion.set(version)
    archiveClassifier.set('')
    if (System.getenv("OUTPUT_DIR") != null) {
        destinationDirectory.set(file(System.getenv("OUTPUT_DIR")))
    } else {
        destinationDirectory.set(file("$projectDir/../build/libs"))
    }
    relocate("de.exlll.configlib", "me.typical.lib.configlib")
    relocate("dev.jorel.commandapi", "me.typical.lib.commandapi")
    relocate("com.tcoded.folialib", "me.typical.lib.folialib")
    relocate("me.devnatan.inventoryframework", "me.typical.lib.inventoryframework")
    relocate("org.json.json", "me.typical.lib.json")
    relocate("io.github.retrooper.packetevents", "me.typical.lib.io.packetevents")
    relocate("com.github.retrooper.packetevents", "me.typical.lib.com.packetevents")
    relocate("net.wesjd.anvilgui", "me.typical.lib.anvilgui")
    relocate("kotlin", "me.typical.lib.kotlin")
    relocate("vn.payos", "me.typical.lib.payos")
}
tasks.register('sendRconCommand') {
    description = 'Sends "res" command to the Minecraft server via RCON'
    doLast {
        def host = 'localhost'          // your server IP or hostname
        def port = 25575                // your RCON port
        def password = '123123123' // your RCON password
        def command = 'res' // the command you want to send
        // Send the command and capture the response
        Rcon rcon = null
        try {
            rcon = new Rcon(host, port, password)
            String response = rcon.command(command)
            println "RCON Response: $response"

        } catch (SocketException e) {
            // Connection reset is expected when the server stops itself
            println "🔌 Connection reset by server (expected if it just stopped). Ignoring."
        } catch (Exception e) {
            // Other RCON or IO errors
            println "❗ Unexpected error sending RCON command: ${e.class.simpleName}: ${e.message}"
        } finally {
            if (rcon != null) {
                try {
                    rcon.close()
                } catch (Exception ignored) {
                }
            }
        }
        println "⏱ Waiting 5 second..."
        Thread.sleep(5000)
    }
}
processResources {
    def props = [version: version]
    inputs.properties props
    filteringCharset 'UTF-8'
    filesMatching('plugin.yml') {
        expand props
    }
}
tasks.build {
    dependsOn(shadowJar)
}
