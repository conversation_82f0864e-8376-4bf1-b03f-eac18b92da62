package org.simpmc.simppay.handler.data;

import org.simpmc.simppay.handler.PaymentHandler;
import org.simpmc.simppay.handler.banking.payos.PayosHandlerV2;
import org.simpmc.simppay.handler.banking.redis.RedisHandler;

public enum BankAPI {
    PAYOS(PayosHandlerV2.class),
    REDIS(RedisHandler.class);

    public final Class<?> handlerClass;

    BankAPI(Class<? extends PaymentHandler> handlerClass) {
        this.handlerClass = handlerClass;
    }
}
