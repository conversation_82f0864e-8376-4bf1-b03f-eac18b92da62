# Hướng dẫn tùy chỉnh cho GitHub Copilot - Dự án Plugin Minecraft

Các hướng dẫn này nhằm mục đích định hướng GitHub Copilot để tạo ra các phản hồi phù hợp với quy trình làm việc, công cụ và đặc điểm dự án của nhóm.

## Hướng dẫn chung

-   Luôn tuân thủ nghiêm ngặt các quy định và thông số kỹ thuật được nêu trong file thiết kế chi tiết của dự án.
-   <PERSON><PERSON><PERSON> bảo rằng tất cả các giá trị cấu hình và dữ liệu động không được mã hóa cứng (hardcode) trong code. Thay vào đó, chúng nên được quản lý thông qua các file cấu hình hoặc các phương pháp khác phù hợp.
-   Tất cả các phản hồi và nội dung được tạo ra phải bằng tiếng Việt.

## Hướng dẫn cụ thể

### Java Instructions

-   Khi phát triển các plugin Java, hãy sử dụng Minecraft API phiên bản 1.21.
-   Đảm bảo code Java tuân thủ các nguyên tắc thiết kế được mô tả trong file thiết kế chi tiết.
-   Tránh mã hóa cứng các giá trị trong code Java.

### Placeholder Instructions

-   Dự án này sử dụng PlaceholderAPI để quản lý các placeholders.
-   Đối với các script Skript, hãy sử dụng addon Skript-Placeholder để xử lý các placeholders.
-   Đảm bảo dự án được hook chính xác với Skript để cho phép tương tác placeholder liền mạch.

### API Rule Instructions

-   Việc sử dụng API phải nhất quán với các yêu cầu và cấu trúc được định nghĩa trong file thiết kế chi tiết.
-   Không được mã hóa cứng các khóa API, điểm cuối hoặc bất kỳ tham số liên quan đến API nào khác.