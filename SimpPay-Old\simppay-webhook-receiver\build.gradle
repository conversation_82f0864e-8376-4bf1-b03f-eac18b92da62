buildscript {
    ext {
        joobyVersion = "3.7.0"
    }
}

plugins {
    id "application"
    id "io.jooby.run" version "${joobyVersion}"
    id "io.spring.dependency-management" version "1.1.0"
    id "com.google.osdetector" version "1.7.3"
    id 'com.gradleup.shadow' version '8.3.3'
}

group "org.simpmc.napthe"
version "1.0.0"
mainClassName = "org.simpmc.napthe.App"

repositories {
    mavenLocal()
    mavenCentral()
}

dependencyManagement {
    imports {
        mavenBom "io.jooby:jooby-bom:$joobyVersion"
    }
}

dependencies {

    compileOnly 'com.j256.ormlite:ormlite-jdbc:6.1'
    implementation 'io.jooby:jooby-hikari:3.6.1'
    compileOnly 'com.zaxxer:HikariCP:5.0.1'

    compileOnly 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.projectlombok:lombok:1.18.36'
    implementation 'io.jooby:jooby-redis:3.6.1'
    annotationProcessor "io.jooby:jooby-apt"
    implementation "io.jooby:jooby-netty"
    implementation "io.jooby:jooby-logback"

    testImplementation "org.junit.jupiter:junit-jupiter-api:5.11.4"
    testImplementation "org.junit.jupiter:junit-jupiter-engine:5.11.4"
    testImplementation "io.jooby:jooby-test"
    testImplementation "com.squareup.okhttp3:okhttp:4.12.0"
}

test {
    useJUnitPlatform()
}

/** Java debug information: */
tasks.withType(JavaCompile) {
    options.compilerArgs += [
            '-parameters',
            '-Ajooby.incremental=true',
            '-Ajooby.services=true',
            '-Ajooby.debug=false'
    ]
    options.debug = true
}

shadowJar {
    mergeServiceFiles()
}

joobyRun {
    mainClass = mainClassName
    restartExtensions = ["conf", "properties", "class"]
    compileExtensions = ["java"]
    port = 8080
}
