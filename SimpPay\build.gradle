buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        // Pull in the RCON-Java client
        classpath 'org.glavo:rcon-java:3.0'
    }
}
plugins {
    id 'java'
    // Note: We apply the shadow plugin only in the modules that need it.
    id 'com.gradleup.shadow' version '8.3.5' apply false
}
jar {
    enabled = false
}
group = 'org.simpmc'
version = '1.3.0-SoulMC'

subprojects {
    apply plugin: 'java-library'

    repositories {
        mavenCentral()
        maven {
            url = "https://repo.opencollab.dev/main/"
        }
        maven {
            name = "papermc-repo"
            url = "https://repo.papermc.io/repository/maven-public/"
        }
        maven {
            name = "sonatype"
            url = "https://oss.sonatype.org/content/groups/public/"
        }
        maven {
            name = "jitpack"
            url = "https://jitpack.io"
        }
        maven {
            url = "https://repo.codemc.io/repository/maven-releases/"
        }
        maven {
            url = "https://repo.codemc.io/repository/maven-snapshots/"
        }
        maven {
            url = 'https://repo.extendedclip.com/releases/'
        }
        maven {
            url = 'https://oss.sonatype.org/content/repositories/snapshots'
        }
        maven {
            url = 'https://repo.rosewooddev.io/repository/public/'
        }
        maven {
            url = 'https://libraries.minecraft.net/'
        }
        maven {
            name = "tcoded-releases"
            url = "https://repo.tcoded.com/releases"
        }
        maven {
            name = "nightexpress-releases"
            url = "https://repo.nightexpressdev.com/releases"
        }
    }
}

// Set the Java version for all subprojects
def targetJavaVersion = 21
java {
    def javaVersion = JavaVersion.toVersion(targetJavaVersion)
    sourceCompatibility = javaVersion
    targetCompatibility = javaVersion
    if (JavaVersion.current() < javaVersion) {
        toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
    }
}

subprojects {
    apply plugin: 'java-library'
    
    java {
        def javaVersion = JavaVersion.toVersion(targetJavaVersion)
        sourceCompatibility = javaVersion
        targetCompatibility = javaVersion
        if (JavaVersion.current() < javaVersion) {
            toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
        }
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
    if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
        options.release.set(targetJavaVersion)
    }
}

allprojects {
    tasks.withType(JavaCompile).configureEach {
        options.encoding = 'UTF-8'
        if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
            options.release.set(targetJavaVersion)
        }
    }
}