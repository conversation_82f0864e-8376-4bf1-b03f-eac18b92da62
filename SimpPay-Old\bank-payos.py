#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test PayOS API
Author: SimpMC
Date: 2025-07-06
"""

from payos import PayOS, ItemData, PaymentData
import os
import json
import time
import sys
import argparse
from datetime import datetime, timedelta

class PayOSTestScript:
    def __init__(self):
        """Khởi tạo PayOS client"""
        # Thông tin PayOS được tích hợp sẵn
        self.client_id = "81d620ac-aa19-41d6-a4ef-c971796212ef"
        self.api_key = "83981552-afd5-4678-a4cd-d52e2c11664c"
        self.checksum_key = "af4e109fd860918a37b354db09df907f5c1b5b00de72d11a6cf49d91829490f3"
        
        try:
            # Khởi tạo PayOS client
            self.payOS = PayOS(
                client_id=self.client_id,
                api_key=self.api_key,
                checksum_key=self.checksum_key
            )
            
            print("✅ Khởi tạo PayOS thành công!")
            print(f"Client ID: {self.client_id}")
            
        except Exception as e:
            print(f"❌ Lỗi khởi tạo PayOS: {e}")
            self.payOS = None
    
    def create_test_payment_link(self, order_code=None, amount=25000):
        """Tạo link thanh toán test"""
        if not self.payOS:
            print("❌ PayOS chưa được khởi tạo!")
            return None
            
        try:
            print("\n🔄 Đang tạo link thanh toán...")
            
            # Tạo order code unique nếu không được cung cấp
            if order_code is None:
                order_code = int(time.time())
            
            # Validate amount (max 1000)
            if amount > 1000:
                print(f"❌ Số tiền vượt quá giới hạn! Max: 1000, Current: {amount}")
                return None
            
            # Tạo item data
            item1 = ItemData(
                name="Test Item", 
                quantity=1, 
                price=amount
            )
            
            # Tạo payment data
            payment_data = PaymentData(
                orderCode=order_code,
                amount=amount,
                description=f"Test {amount}VND",
                items=[item1],
                cancelUrl="http://localhost:8000/cancel",
                returnUrl="http://localhost:8000/success"
            )
            
            # Tạo link thanh toán
            result = self.payOS.createPaymentLink(paymentData=payment_data)
            
            print("✅ Tạo link thanh toán thành công!")
            print(f"Order Code: {result.orderCode}")
            print(f"Amount: {result.amount:,} VND")
            print(f"Payment Link ID: {result.paymentLinkId}")
            print(f"Status: {result.status}")
            print(f"Checkout URL: {result.checkoutUrl}")
            print(f"QR Code: {result.qrCode}")
            
            return result
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo link thanh toán: {e}")
            return None
    
    def get_payment_info(self, order_id):
        """Lấy thông tin thanh toán"""
        if not self.payOS:
            print("❌ PayOS chưa được khởi tạo!")
            return None
            
        try:
            print(f"\n🔄 Đang lấy thông tin thanh toán cho Order ID: {order_id}")
            
            payment_info = self.payOS.getPaymentLinkInformation(orderId=order_id)
            
            print("✅ Lấy thông tin thanh toán thành công!")
            print(f"Payment Link ID: {payment_info.id}")
            print(f"Order Code: {payment_info.orderCode}")
            print(f"Amount: {payment_info.amount:,} VND")
            print(f"Amount Paid: {payment_info.amountPaid:,} VND")
            print(f"Amount Remaining: {payment_info.amountRemaining:,} VND")
            print(f"Status: {payment_info.status}")
            print(f"Created At: {payment_info.createdAt}")
            
            if payment_info.transactions:
                print(f"Transactions count: {len(payment_info.transactions)}")
                for i, transaction in enumerate(payment_info.transactions):
                    print(f"  Transaction {i+1}:")
                    print(f"    Reference: {transaction.reference}")
                    print(f"    Amount: {transaction.amount:,} VND")
                    print(f"    DateTime: {transaction.transactionDateTime}")
            
            if payment_info.cancellationReason:
                print(f"Cancellation Reason: {payment_info.cancellationReason}")
                print(f"Canceled At: {payment_info.canceledAt}")
            
            return payment_info
            
        except Exception as e:
            print(f"❌ Lỗi khi lấy thông tin thanh toán: {e}")
            return None
    
    def cancel_payment_link(self, order_id, reason="Test cancellation"):
        """Hủy link thanh toán"""
        if not self.payOS:
            print("❌ PayOS chưa được khởi tạo!")
            return None
            
        try:
            print(f"\n🔄 Đang hủy link thanh toán cho Order ID: {order_id}")
            
            payment_info = self.payOS.cancelPaymentLink(
                orderId=order_id,
                cancellationReason=reason
            )
            
            print("✅ Hủy link thanh toán thành công!")
            print(f"Order Code: {payment_info.orderCode}")
            print(f"Status: {payment_info.status}")
            print(f"Cancellation Reason: {payment_info.cancellationReason}")
            print(f"Canceled At: {payment_info.canceledAt}")
            
            return payment_info
            
        except Exception as e:
            print(f"❌ Lỗi khi hủy link thanh toán: {e}")
            return None
    
    def confirm_webhook(self, webhook_url):
        """Xác thực webhook URL"""
        if not self.payOS:
            print("❌ PayOS chưa được khởi tạo!")
            return None
            
        try:
            print(f"\n🔄 Đang xác thực webhook URL: {webhook_url}")
            
            result = self.payOS.confirmWebhook(webhook_url)
            
            print("✅ Xác thực webhook thành công!")
            print(f"Result: {result}")
            
            return result
            
        except Exception as e:
            print(f"❌ Lỗi khi xác thực webhook: {e}")
            return None
    
    def verify_webhook_data(self):
        """Test xác minh dữ liệu webhook"""
        if not self.payOS:
            print("❌ PayOS chưa được khởi tạo!")
            return None
            
        try:
            print("\n🔄 Đang test xác minh dữ liệu webhook...")
            
            # Dữ liệu webhook mẫu
            webhook_body = {
                "code": "00",
                "desc": "success",
                "success": True,
                "data": {
                    "accountNumber": "**********",
                    "amount": 25000,
                    "description": "Ma giao dich thu nghiem",
                    "reference": "****************",
                    "transactionDateTime": "2023-11-21 15:20:34",
                    "virtualAccountNumber": "",
                    "counterAccountBankId": "",
                    "counterAccountBankName": "",
                    "counterAccountName": "",
                    "counterAccountNumber": "",
                    "virtualAccountName": "",
                    "orderCode": 52422,
                    "currency": "VND",
                    "paymentLinkId": "b646a39ca8654d8fa03e0dc8bec7264c",
                    "code": "00",
                    "desc": "success"
                },
                "signature": "1f2eb76896a3a8e10e1f560bed4087f788c5d654af6d0a1d394351806a34d6dd"
            }
            
            webhook_data = self.payOS.verifyPaymentWebhookData(webhook_body)
            
            print("✅ Xác minh webhook data thành công!")
            print(f"Order Code: {webhook_data.orderCode}")
            print(f"Amount: {webhook_data.amount:,} VND")
            print(f"Description: {webhook_data.description}")
            print(f"Reference: {webhook_data.reference}")
            print(f"Transaction DateTime: {webhook_data.transactionDateTime}")
            print(f"Payment Link ID: {webhook_data.paymentLinkId}")
            print(f"Code: {webhook_data.code}")
            print(f"Desc: {webhook_data.desc}")
            
            return webhook_data
            
        except Exception as e:
            print(f"❌ Lỗi khi xác minh webhook data: {e}")
            return None
    
    def run_full_test(self, amount=25000):
        """Chạy test đầy đủ tất cả chức năng"""
        if not self.payOS:
            print("❌ PayOS chưa được khởi tạo!")
            return
            
        print("🚀 Bắt đầu test PayOS API...")
        print("=" * 50)
        
        # Test 1: Tạo link thanh toán
        payment_result = self.create_test_payment_link(amount=amount)
        if not payment_result:
            return
        
        order_code = payment_result.orderCode
        
        # Test 2: Lấy thông tin thanh toán
        payment_info = self.get_payment_info(order_code)
        
        # Test 3: Test webhook verification
        self.verify_webhook_data()
        
        # Test 4: Confirm webhook (tùy chọn - cần URL thật)
        # self.confirm_webhook("https://your-webhook-url.com/webhook")
        
        # Test 5: Hủy link thanh toán
        print("\n⏳ Đợi 5 giây trước khi hủy link thanh toán...")
        time.sleep(5)
        self.cancel_payment_link(order_code, "Test completed - cancelling payment")
        
        # Test 6: Kiểm tra lại thông tin sau khi hủy
        print("\n🔄 Kiểm tra lại thông tin sau khi hủy...")
        final_payment_info = self.get_payment_info(order_code)
        
        print("\n✅ Hoàn thành test PayOS API!")
        print("=" * 50)

def main():
    """Hàm main với hỗ trợ command line"""
    parser = argparse.ArgumentParser(description='PayOS API Test Script')
    parser.add_argument('amount', nargs='?', type=int, default=None, 
                       help='Số tiền thanh toán (tối đa 1000 VND)')
    parser.add_argument('--menu', '-m', action='store_true',
                       help='Hiển thị menu tương tác')
    
    args = parser.parse_args()
    
    print("PayOS API Test Script")
    print("=" * 30)
    
    # Tạo instance PayOS
    test_script = PayOSTestScript()
    
    if not test_script.payOS:
        return
    
    # Nếu có số tiền được truyền vào từ command line
    if args.amount is not None:
        if args.amount > 1000:
            print(f"❌ Số tiền vượt quá giới hạn! Max: 1000, Current: {args.amount}")
            return
        
        print(f"🚀 Tạo link thanh toán với số tiền: {args.amount:,} VND")
        result = test_script.create_test_payment_link(amount=args.amount)
        if result:
            print(f"\n💡 Để kiểm tra thông tin thanh toán:")
            print(f"python bank-payos.py --check {result.orderCode}")
        return
    
    # Nếu được yêu cầu hiển thị menu hoặc không có tham số
    if args.menu or len(sys.argv) == 1:
        show_interactive_menu(test_script)
    else:
        parser.print_help()

def show_interactive_menu(test_script):
    """Hiển thị menu tương tác"""
    while True:
        print("\n" + "=" * 40)
        print("PayOS Test Menu:")
        print("1. Chạy test đầy đủ")
        print("2. Tạo link thanh toán với số tiền tùy chỉnh")
        print("3. Lấy thông tin thanh toán")
        print("4. Hủy link thanh toán")
        print("5. Test verify webhook data")
        print("6. Confirm webhook URL")
        print("0. Thoát")
        print("=" * 40)
        
        choice = input("Chọn chức năng (0-6): ").strip()
        
        if choice == "0":
            print("👋 Tạm biệt!")
            break
        elif choice == "1":
            amount = input("Nhập số tiền (default=25000, max=1000): ").strip()
            try:
                amount = int(amount) if amount else 25000
                if amount > 1000:
                    print("❌ Số tiền vượt quá 1000 VND!")
                    continue
                test_script.run_full_test(amount=amount)
            except ValueError:
                print("❌ Số tiền phải là số!")
        elif choice == "2":
            amount = input("Nhập số tiền (max=1000): ").strip()
            try:
                amount = int(amount)
                if amount > 1000:
                    print("❌ Số tiền vượt quá 1000 VND!")
                    continue
                test_script.create_test_payment_link(amount=amount)
            except ValueError:
                print("❌ Số tiền phải là số!")
        elif choice == "3":
            order_id = input("Nhập Order ID: ").strip()
            if order_id:
                try:
                    order_id = int(order_id)
                    test_script.get_payment_info(order_id)
                except ValueError:
                    print("❌ Order ID phải là số!")
        elif choice == "4":
            order_id = input("Nhập Order ID: ").strip()
            reason = input("Nhập lý do hủy (tùy chọn): ").strip() or "Manual cancellation"
            if order_id:
                try:
                    order_id = int(order_id)
                    test_script.cancel_payment_link(order_id, reason)
                except ValueError:
                    print("❌ Order ID phải là số!")
        elif choice == "5":
            test_script.verify_webhook_data()
        elif choice == "6":
            webhook_url = input("Nhập Webhook URL: ").strip()
            if webhook_url:
                test_script.confirm_webhook(webhook_url)
            else:
                print("❌ Webhook URL không được để trống!")
        else:
            print("❌ Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()