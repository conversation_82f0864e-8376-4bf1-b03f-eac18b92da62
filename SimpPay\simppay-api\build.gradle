plugins {
    id 'java-library'
}

dependencies {
    // Shared dependencies can be placed here.
    // For example, Lombok if you use it across modules:
    compileOnly 'com.j256.ormlite:ormlite-jdbc:6.1'
//    implementation 'org.xerial:sqlite-jdbc:3.42.0.0'
//    implementation 'com.h2database:h2:2.3.232'
    compileOnly 'com.zaxxer:HikariCP:6.3.0'

    compileOnly 'com.google.code.gson:gson:2.13.1'
    compileOnly 'commons-codec:commons-codec:1.18.0'

    compileOnly 'org.projectlombok:lombok:1.18.38'
    annotationProcessor 'org.projectlombok:lombok:1.18.38'
}