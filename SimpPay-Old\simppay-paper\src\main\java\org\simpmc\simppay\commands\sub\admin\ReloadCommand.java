package org.simpmc.simppay.commands.sub.admin;

import dev.jorel.commandapi.CommandAPICommand;
import dev.jorel.commandapi.executors.CommandArguments;
import org.bukkit.command.CommandSender;
import org.simpmc.simppay.SPPlugin;
import org.simpmc.simppay.config.ConfigManager;
import org.simpmc.simppay.config.types.MessageConfig;
import org.simpmc.simppay.util.MessageUtil;

public class ReloadCommand {
    public static CommandAPICommand commandCreate() {
        return new CommandAPICommand("reload")
                .withPermission("simppay.admin.reload")
                .executes(ReloadCommand::execute);
    }

    public static void execute(CommandSender sender, CommandArguments args) {
        SPPlugin plugin = SPPlugin.getInstance();
        plugin.getFoliaLib().getScheduler().runAsync(task -> {
            // Clean up milestone bossbars before reloading (async to prevent lag)
            if (plugin.getMilestoneService() != null) {
                plugin.getMilestoneService().cleanup();
            }
            
            plugin.getConfigManager().reloadAll();
            
            // Reload milestones after config reload
            if (plugin.getMilestoneService() != null) {
                plugin.getMilestoneService().loadAllMilestones();
            }
            
            MessageConfig messageConfig = ConfigManager.getInstance().getConfig(MessageConfig.class);
            MessageUtil.sendMessage(sender, messageConfig.configReloaded);
        });
    }
}
