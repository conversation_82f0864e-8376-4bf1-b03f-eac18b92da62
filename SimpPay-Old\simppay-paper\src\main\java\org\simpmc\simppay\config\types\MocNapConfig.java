package org.simpmc.simppay.config.types;

import de.exlll.configlib.Configuration;
import net.kyori.adventure.bossbar.BossBar;
import org.simpmc.simppay.config.types.data.BossBarConfig;
import org.simpmc.simppay.config.types.data.MilestoneConfig;
import org.simpmc.simppay.data.milestone.MilestoneType;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

@Configuration
public class MocNapConfig {
    public Map<MilestoneType, List<MilestoneConfig>> mocnap = new HashMap<>();
    
    public MocNapConfig() {
        if (mocnap.isEmpty()) {
            initializeDefaultConfig();
        }
    }
    
    private void initializeDefaultConfig() {
        ArrayList<MilestoneConfig> allMilestones = new ArrayList<>();
        allMilestones.add(new MilestoneConfig(
                MilestoneType.ALL, 100000,
                new BossBarConfig(false, "MocNap 100k alltime", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 100k toàn thời gian"); }}
        ));

        allMilestones.add(new MilestoneConfig(
                MilestoneType.ALL, 200000,
                new BossBarConfig(false, "MocNap 200k alltime", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 200k toàn thời gian"); }}
        ));
        mocnap.put(MilestoneType.ALL, allMilestones);
        
        // DAILY milestones
        ArrayList<MilestoneConfig> dailyMilestones = new ArrayList<>();
        dailyMilestones.add(new MilestoneConfig(
                MilestoneType.DAILY, 100000,
                new BossBarConfig(false, "MocNap 100k daily", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 100k daily"); }}
        ));

        dailyMilestones.add(new MilestoneConfig(
                MilestoneType.DAILY, 200000,
                new BossBarConfig(false, "MocNap 200k daily", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 200k daily"); }}
        ));
        mocnap.put(MilestoneType.DAILY, dailyMilestones);
        
        // WEEKLY milestones  
        ArrayList<MilestoneConfig> weeklyMilestones = new ArrayList<>();
        weeklyMilestones.add(new MilestoneConfig(
                MilestoneType.WEEKLY, 100000,
                new BossBarConfig(false, "MocNap 100k weekly", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 100k weekly"); }}
        ));

        weeklyMilestones.add(new MilestoneConfig(
                MilestoneType.WEEKLY, 200000,
                new BossBarConfig(false, "MocNap 200k weekly", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 200k weekly"); }}
        ));
        mocnap.put(MilestoneType.WEEKLY, weeklyMilestones);
        
        // MONTHLY milestones
        ArrayList<MilestoneConfig> monthlyMilestones = new ArrayList<>();
        monthlyMilestones.add(new MilestoneConfig(
                MilestoneType.MONTHLY, 100000,
                new BossBarConfig(false, "MocNap 100k monthly", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 100k monthly"); }}
        ));

        monthlyMilestones.add(new MilestoneConfig(
                MilestoneType.MONTHLY, 200000,
                new BossBarConfig(false, "MocNap 200k monthly", BossBar.Color.YELLOW, BossBar.Overlay.PROGRESS),
                new ArrayList<String>() {{ add("tell %player_name% Đã vượt mức pickleball 200k monthly"); }}
        ));
        mocnap.put(MilestoneType.MONTHLY, monthlyMilestones);
    }
}
